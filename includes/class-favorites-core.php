<?php
/**
 * WP Favorites Core Class
 * 
 * Main functionality and coordination between components
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Core {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Admin instance
     */
    public $admin;
    
    /**
     * Frontend instance
     */
    public $frontend;
    
    /**
     * AJAX instance
     */
    public $ajax;
    
    /**
     * Cache instance
     */
    public $cache;
    
    /**
     * Performance instance
     */
    public $performance;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        // Components will be initialized via the 'init' hook
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize components
        add_action('init', array($this, 'init_components'), 10);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // AJAX hooks are handled by the AJAX class to avoid duplication

        // Initialize WooCommerce hooks based on admin settings
        add_action('init', array($this, 'init_woocommerce_hooks'), 20);

        // Shortcode
        add_shortcode('wp_favorites_list', array($this, 'favorites_list_shortcode'));
    }

    /**
     * Initialize WooCommerce hooks based on admin settings
     */
    public function init_woocommerce_hooks() {
        if (!function_exists('WC')) {
            return;
        }

        // Get display location settings
        $display_locations = get_option('wp_favorites_display_locations', array(
            'shop_loop' => true,
            'single_product' => true,
            'related_products' => true,
            'cross_sells' => true,
            'up_sells' => true,
            'widgets' => true,
            'search_results' => true,
            'category_pages' => true
        ));

        // Shop page and category loops
        if (!empty($display_locations['shop_loop']) || !empty($display_locations['category_pages'])) {
            add_action('woocommerce_after_shop_loop_item', array($this, 'add_favorite_button_with_wrapper'), 15);
        }

        // Single product pages
        if (!empty($display_locations['single_product'])) {
            add_action('woocommerce_single_product_summary', array($this, 'add_favorite_button_with_wrapper'), 35);
        }

        // Related products
        if (!empty($display_locations['related_products'])) {
            add_action('woocommerce_output_related_products_args', array($this, 'ensure_related_products_hooks'));
            add_action('woocommerce_after_single_product_summary', array($this, 'add_related_products_hooks'), 19);
        }

        // Cross-sells (cart page)
        if (!empty($display_locations['cross_sells'])) {
            add_action('woocommerce_cart_collaterals', array($this, 'add_cross_sell_hooks'), 9);
        }

        // Up-sells (single product page)
        if (!empty($display_locations['up_sells'])) {
            add_action('woocommerce_after_single_product_summary', array($this, 'add_up_sell_hooks'), 14);
        }

        // Widgets and shortcodes
        if (!empty($display_locations['widgets'])) {
            add_action('woocommerce_widget_product_item_start', array($this, 'add_widget_favorite_button'), 10);
        }

        // Search results (uses same loop as shop)
        if (!empty($display_locations['search_results'])) {
            // Already covered by shop_loop hook
        }

        // Add hooks for any product loop context
        add_action('woocommerce_before_shop_loop_item', array($this, 'setup_product_loop_context'), 5);
    }
    
    /**
     * Initialize components
     */
    public function init_components() {
        // Initialize cache
        if (class_exists('WP_Favorites_Cache')) {
            $this->cache = new WP_Favorites_Cache();
        }
        
        // Initialize performance
        if (class_exists('WP_Favorites_Performance')) {
            $this->performance = new WP_Favorites_Performance();
        }
        
        // Admin is initialized in main plugin file to avoid duplication
        // $this->admin will be set externally if needed
        
        // Initialize frontend
        if (!is_admin() && class_exists('WP_Favorites_Frontend')) {
            $this->frontend = new WP_Favorites_Frontend();
        }
        
        // Initialize AJAX
        if (class_exists('WP_Favorites_Ajax')) {
            $this->ajax = new WP_Favorites_Ajax();
        }
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only load on WooCommerce pages or favorites page
        if (!$this->should_load_frontend_assets()) {
            return;
        }
        
        // CSS
        wp_enqueue_style(
            'wp-favorites-frontend',
            WP_FAVORITES_PLUGIN_URL . 'assets/css/frontend-style.css',
            array(),
            WP_FAVORITES_VERSION
        );
        
        // JavaScript
        wp_enqueue_script(
            'wp-favorites-frontend',
            WP_FAVORITES_PLUGIN_URL . 'assets/js/frontend-script.js',
            array('jquery', 'wp-i18n'),
            WP_FAVORITES_VERSION,
            true
        );
        
        // Prepare localization data
        $localize_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_favorites_nonce'),
            'messages' => $this->get_frontend_messages(),
            'settings' => array(
                'notification_duration' => get_option('wp_favorites_notification_duration', 3000),
                'enable_notifications' => get_option('wp_favorites_enable_notifications', true),
            )
        );

        // Apply filter to allow frontend class to add user data
        $localize_data = apply_filters('wp_favorites_localize_data', $localize_data);

        // Localize script with translatable messages
        wp_localize_script('wp-favorites-frontend', 'wpFavoritesData', $localize_data);
        
        // Set up script translations
        wp_set_script_translations('wp-favorites-frontend', 'wp-favorites');
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin admin pages
        if (strpos($hook, 'wp-favorites') === false) {
            return;
        }
        
        // CSS
        wp_enqueue_style(
            'wp-favorites-admin',
            WP_FAVORITES_PLUGIN_URL . 'admin/css/admin-style.css',
            array(),
            WP_FAVORITES_VERSION
        );
        
        // JavaScript
        wp_enqueue_script(
            'wp-favorites-admin',
            WP_FAVORITES_PLUGIN_URL . 'admin/js/admin-script.js',
            array('jquery', 'wp-i18n'),
            WP_FAVORITES_VERSION,
            true
        );
        
        // Media uploader
        wp_enqueue_media();
    }
    
    /**
     * Check if frontend assets should be loaded
     */
    private function should_load_frontend_assets() {
        // Only load if WooCommerce is active
        if (!function_exists('WC')) {
            return false;
        }

        // WooCommerce pages
        if (function_exists('is_woocommerce') && is_woocommerce()) {
            return true;
        }

        // Shop page
        if (function_exists('is_shop') && is_shop()) {
            return true;
        }

        // Product category/tag pages
        if (function_exists('is_product_category') && is_product_category()) {
            return true;
        }

        if (function_exists('is_product_tag') && is_product_tag()) {
            return true;
        }

        // Cart page (for cross-sells)
        if (function_exists('is_cart') && is_cart()) {
            return true;
        }

        // Favorites page
        $favorites_page_id = get_option('wp_favorites_page_id', 0);
        if ($favorites_page_id && is_page($favorites_page_id)) {
            return true;
        }

        // Pages with favorites shortcode
        global $post;
        if ($post && has_shortcode($post->post_content, 'wp_favorites_list')) {
            return true;
        }

        // Check if page has WooCommerce shortcodes that might display products
        if ($post && (
            has_shortcode($post->post_content, 'products') ||
            has_shortcode($post->post_content, 'recent_products') ||
            has_shortcode($post->post_content, 'featured_products') ||
            has_shortcode($post->post_content, 'sale_products') ||
            has_shortcode($post->post_content, 'best_selling_products') ||
            has_shortcode($post->post_content, 'top_rated_products') ||
            has_shortcode($post->post_content, 'product_category') ||
            has_shortcode($post->post_content, 'product_categories')
        )) {
            return true;
        }

        // Don't load on homepage unless it's the shop page
        if (is_home() || is_front_page()) {
            $shop_page_id = function_exists('wc_get_page_id') ? wc_get_page_id('shop') : 0;
            if ($shop_page_id && (is_page($shop_page_id) || get_the_ID() === $shop_page_id)) {
                return true;
            }
            return false;
        }

        return false;
    }
    
    /**
     * Get frontend translatable messages
     */
    private function get_frontend_messages() {
        return array(
            'success' => array(
                'added' => __('Product added to favorites', 'wp-favorites'),
                'removed' => __('Product removed from favorites', 'wp-favorites'),
                'updated' => __('Favorites updated successfully', 'wp-favorites')
            ),
            'error' => array(
                'add_failed' => __('Failed to add product to favorites', 'wp-favorites'),
                'remove_failed' => __('Failed to remove product from favorites', 'wp-favorites'),
                'not_found' => __('Product not found', 'wp-favorites'),
                'login_required' => __('Please log in to use favorites', 'wp-favorites')
            ),
            'info' => array(
                'empty_list' => __('Your favorites list is empty', 'wp-favorites'),
                'loading' => __('Loading favorites...', 'wp-favorites'),
                'no_products' => __('No products match your criteria', 'wp-favorites')
            ),
            'confirm' => array(
                'remove' => __('Are you sure you want to remove this product from favorites?', 'wp-favorites'),
                'clear_all' => __('Are you sure you want to clear all favorites?', 'wp-favorites')
            )
        );
    }
    
    /**
     * Add favorite button to products with wrapper for positioning
     */
    public function add_favorite_button_with_wrapper() {
        global $product;

        if (!$product || !is_a($product, 'WC_Product')) {
            return;
        }

        // Get positioning and size settings
        $position = get_option('wp_favorites_icon_position', 'top-right');
        $size = get_option('wp_favorites_icon_size', 'medium');
        $context = $this->get_current_product_context();

        $product_id = $product->get_id();
        $user_id = get_current_user_id();
        $is_favorite = $this->is_favorite($user_id, $product_id);

        $button_class = 'wp-favorites-button';
        $button_class .= $is_favorite ? ' is-favorite' : '';
        $button_class .= ' position-' . esc_attr($position);
        $button_class .= ' size-' . esc_attr($size);
        $button_class .= ' context-' . esc_attr($context);

        $icon_html = $this->get_favorite_icon($is_favorite);
        $title = $is_favorite
            ? __('Remove from favorites', 'wp-favorites')
            : __('Add to favorites', 'wp-favorites');

        // Wrapper for better positioning control
        echo '<div class="wp-favorites-wrapper">';
        echo sprintf(
            '<button class="%s" data-product-id="%d" title="%s" aria-label="%s">%s</button>',
            esc_attr($button_class),
            esc_attr($product_id),
            esc_attr($title),
            esc_attr($title),
            $icon_html
        );
        echo '</div>';
    }

    /**
     * Legacy method for backward compatibility
     */
    public function add_favorite_button() {
        $this->add_favorite_button_with_wrapper();
    }

    /**
     * Get current product display context
     */
    private function get_current_product_context() {
        if (is_single() && is_product()) {
            return 'single-product';
        } elseif (is_shop()) {
            return 'shop';
        } elseif (is_product_category()) {
            return 'category';
        } elseif (is_product_tag()) {
            return 'tag';
        } elseif (is_search()) {
            return 'search';
        } elseif (is_cart()) {
            return 'cart';
        } elseif (doing_action('woocommerce_output_related_products_args')) {
            return 'related';
        } elseif (doing_action('woocommerce_cart_collaterals')) {
            return 'cross-sell';
        } elseif (doing_action('woocommerce_after_single_product_summary')) {
            return 'up-sell';
        } elseif (is_widget()) {
            return 'widget';
        }

        return 'general';
    }

    /**
     * Setup product loop context for better positioning
     */
    public function setup_product_loop_context() {
        // Add CSS class to product loop items for context-aware styling
        add_filter('woocommerce_post_class', array($this, 'add_product_context_class'), 10, 2);
    }

    /**
     * Add context class to product items
     */
    public function add_product_context_class($classes, $product) {
        $context = $this->get_current_product_context();
        $classes[] = 'wp-favorites-context-' . $context;
        return $classes;
    }
    
    /**
     * Check if product is favorite
     */
    public function is_favorite($user_id, $product_id) {
        if (!$user_id || !$product_id) {
            return false;
        }
        
        // Check cache first
        if ($this->cache) {
            $cached = $this->cache->get_user_favorites($user_id);
            if ($cached !== false) {
                return in_array($product_id, $cached);
            }
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND product_id = %d",
            $user_id,
            $product_id
        ));
        
        return $result > 0;
    }
    
    /**
     * Ensure related products have favorite buttons
     */
    public function ensure_related_products_hooks($args) {
        // This will be called when related products are being set up
        return $args;
    }

    /**
     * Add hooks for related products section
     */
    public function add_related_products_hooks() {
        // Hook into related products loop
        add_action('woocommerce_after_shop_loop_item', array($this, 'add_favorite_button_with_wrapper'), 15);
    }

    /**
     * Add hooks for cross-sell products
     */
    public function add_cross_sell_hooks() {
        // Hook into cross-sell products loop
        add_action('woocommerce_after_shop_loop_item', array($this, 'add_favorite_button_with_wrapper'), 15);
    }

    /**
     * Add hooks for up-sell products
     */
    public function add_up_sell_hooks() {
        // Hook into up-sell products loop
        add_action('woocommerce_after_shop_loop_item', array($this, 'add_favorite_button_with_wrapper'), 15);
    }

    /**
     * Add favorite button for widget products
     */
    public function add_widget_favorite_button() {
        // Add button for widget context
        $this->add_favorite_button_with_wrapper();
    }

    /**
     * Get favorite icon HTML
     */
    private function get_favorite_icon($is_favorite = false) {
        $icon_type = $is_favorite ? 'filled' : 'empty';

        // Get custom icon if set
        $custom_icon = get_option('wp_favorites_custom_icon', '');
        if (!empty($custom_icon)) {
            return $custom_icon;
        }

        // Default heart icons (can be customized via admin)
        $icons = array(
            'empty' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>',
            'filled' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>'
        );

        return apply_filters('wp_favorites_icon_html', $icons[$icon_type], $icon_type, $is_favorite);
    }
    
    /**
     * Favorites list shortcode
     */
    public function favorites_list_shortcode($atts) {
        $atts = shortcode_atts(array(
            'columns' => 4,
            'limit' => -1,
            'show_remove' => 'yes'
        ), $atts, 'wp_favorites_list');
        
        if (!is_user_logged_in()) {
            return '<p>' . __('Please log in to view your favorites.', 'wp-favorites') . '</p>';
        }
        
        ob_start();
        
        if ($this->frontend && method_exists($this->frontend, 'render_favorites_list')) {
            $this->frontend->render_favorites_list($atts);
        }
        
        return ob_get_clean();
    }
    

}
