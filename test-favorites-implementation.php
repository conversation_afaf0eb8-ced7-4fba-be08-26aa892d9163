<?php
/**
 * Test script for WP Favorites Universal Implementation
 * 
 * This file helps verify that the favorite icons appear correctly
 * across all WooCommerce product display locations.
 * 
 * Usage: Include this file in your theme's functions.php temporarily
 * or run it as a standalone test.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test WP Favorites Universal Implementation
 */
class WP_Favorites_Implementation_Test {
    
    public function __construct() {
        add_action('wp_footer', array($this, 'add_test_output'));
        add_action('admin_notices', array($this, 'admin_test_notice'));
    }
    
    /**
     * Add test output to frontend
     */
    public function add_test_output() {
        if (!is_admin() && function_exists('WC')) {
            echo '<div id="wp-favorites-test-output" style="position: fixed; bottom: 20px; right: 20px; background: #fff; padding: 15px; border: 1px solid #ccc; border-radius: 5px; z-index: 9999; max-width: 300px; font-size: 12px;">';
            echo '<h4>WP Favorites Test Results</h4>';
            
            // Test current page context
            $context = $this->get_current_context();
            echo '<p><strong>Current Context:</strong> ' . esc_html($context) . '</p>';
            
            // Test settings
            $this->test_settings();
            
            // Test hooks
            $this->test_hooks();
            
            echo '<p><em>This test output will only appear when testing is enabled.</em></p>';
            echo '</div>';
        }
    }
    
    /**
     * Get current page context
     */
    private function get_current_context() {
        if (is_single() && is_product()) {
            return 'Single Product Page';
        } elseif (is_shop()) {
            return 'Shop Page';
        } elseif (is_product_category()) {
            return 'Product Category Page';
        } elseif (is_product_tag()) {
            return 'Product Tag Page';
        } elseif (is_search()) {
            return 'Search Results Page';
        } elseif (is_cart()) {
            return 'Cart Page';
        } elseif (is_home() || is_front_page()) {
            return 'Homepage';
        }
        
        return 'Other Page';
    }
    
    /**
     * Test plugin settings
     */
    private function test_settings() {
        echo '<h5>Settings Test:</h5>';
        
        $display_locations = get_option('wp_favorites_display_locations', array());
        $icon_position = get_option('wp_favorites_icon_position', 'top-right');
        $icon_size = get_option('wp_favorites_icon_size', 'medium');
        
        echo '<ul style="margin: 0; padding-left: 15px;">';
        echo '<li>Display Locations: ' . (is_array($display_locations) ? count($display_locations) . ' configured' : 'Not configured') . '</li>';
        echo '<li>Icon Position: ' . esc_html($icon_position) . '</li>';
        echo '<li>Icon Size: ' . esc_html($icon_size) . '</li>';
        echo '</ul>';
    }
    
    /**
     * Test WooCommerce hooks
     */
    private function test_hooks() {
        echo '<h5>Hooks Test:</h5>';
        
        $hooks_to_test = array(
            'woocommerce_after_shop_loop_item',
            'woocommerce_single_product_summary',
            'woocommerce_after_single_product_summary',
            'woocommerce_cart_collaterals'
        );
        
        echo '<ul style="margin: 0; padding-left: 15px;">';
        foreach ($hooks_to_test as $hook) {
            $has_hook = has_action($hook);
            $status = $has_hook ? '✓ Active' : '✗ Not Active';
            echo '<li>' . esc_html($hook) . ': ' . $status . '</li>';
        }
        echo '</ul>';
    }
    
    /**
     * Admin test notice
     */
    public function admin_test_notice() {
        if (current_user_can('manage_options')) {
            echo '<div class="notice notice-info is-dismissible">';
            echo '<p><strong>WP Favorites Test Mode:</strong> The universal implementation test is active. ';
            echo 'Visit your shop, product pages, and other WooCommerce pages to see test results in the bottom-right corner.</p>';
            echo '</div>';
        }
    }
}

// Initialize test only if WP_DEBUG is enabled or test parameter is present
if ((defined('WP_DEBUG') && WP_DEBUG) || isset($_GET['wp_favorites_test'])) {
    new WP_Favorites_Implementation_Test();
}

/**
 * Quick verification function for developers
 */
function wp_favorites_verify_implementation() {
    $results = array();
    
    // Check if core class exists
    $results['core_class'] = class_exists('WP_Favorites_Core');
    
    // Check if WooCommerce hooks are registered
    $results['shop_hook'] = has_action('woocommerce_after_shop_loop_item');
    $results['single_hook'] = has_action('woocommerce_single_product_summary');
    
    // Check settings
    $results['display_locations'] = get_option('wp_favorites_display_locations', false);
    $results['icon_position'] = get_option('wp_favorites_icon_position', false);
    $results['icon_size'] = get_option('wp_favorites_icon_size', false);
    
    // Check CSS and JS files
    $css_path = WP_FAVORITES_PLUGIN_PATH . 'assets/css/frontend-style.css';
    $js_path = WP_FAVORITES_PLUGIN_PATH . 'assets/js/frontend-script.js';
    
    $results['css_file'] = file_exists($css_path);
    $results['js_file'] = file_exists($js_path);
    
    return $results;
}

/**
 * Debug function to output current favorite button HTML
 */
function wp_favorites_debug_button_html() {
    if (!function_exists('WC') || !is_woocommerce()) {
        return 'Not on a WooCommerce page';
    }
    
    global $product;
    if (!$product || !is_a($product, 'WC_Product')) {
        return 'No product found';
    }
    
    $core = WP_Favorites_Core::get_instance();
    if (!$core) {
        return 'WP Favorites Core not found';
    }
    
    ob_start();
    $core->add_favorite_button_with_wrapper();
    return ob_get_clean();
}
