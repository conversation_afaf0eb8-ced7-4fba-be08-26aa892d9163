/**
 * WP Favorites Frontend Styles
 * Professional, lightweight design with performance focus
 */

/* ==========================================================================
   Favorites Button Styles
   ========================================================================== */

.wp-favorites-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.wp-favorites-button:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.wp-favorites-button:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

.wp-favorites-button svg {
    width: 20px;
    height: 20px;
    color: #666;
    transition: all 0.3s ease;
}

.wp-favorites-button:hover svg {
    color: #e74c3c;
}

.wp-favorites-button.is-favorite svg {
    color: #e74c3c;
    fill: #e74c3c;
}

.wp-favorites-button.is-loading {
    pointer-events: none;
    opacity: 0.7;
}

.wp-favorites-button.is-loading svg {
    animation: wp-favorites-spin 1s linear infinite;
}

/* Button size variations */
.wp-favorites-button.size-small {
    width: 32px;
    height: 32px;
}

.wp-favorites-button.size-small svg {
    width: 16px;
    height: 16px;
}

.wp-favorites-button.size-large {
    width: 48px;
    height: 48px;
}

.wp-favorites-button.size-large svg {
    width: 24px;
    height: 24px;
}

/* Position variations */
.wp-favorites-button.position-top-left {
    top: 10px;
    left: 10px;
    right: auto;
}

.wp-favorites-button.position-bottom-right {
    top: auto;
    bottom: 10px;
    right: 10px;
}

.wp-favorites-button.position-bottom-left {
    top: auto;
    bottom: 10px;
    left: 10px;
    right: auto;
}

/* ==========================================================================
   Toast Notifications
   ========================================================================== */

.wp-favorites-toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    pointer-events: none;
}

.wp-favorites-toast {
    background: #fff;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #007cba;
    max-width: 350px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    position: relative;
}

.wp-favorites-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.wp-favorites-toast.success {
    border-left-color: #27ae60;
}

.wp-favorites-toast.error {
    border-left-color: #e74c3c;
}

.wp-favorites-toast.info {
    border-left-color: #3498db;
}

.wp-favorites-toast.warning {
    border-left-color: #f39c12;
}

.wp-favorites-toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.wp-favorites-toast-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.wp-favorites-toast-icon.success {
    color: #27ae60;
}

.wp-favorites-toast-icon.error {
    color: #e74c3c;
}

.wp-favorites-toast-icon.info {
    color: #3498db;
}

.wp-favorites-toast-icon.warning {
    color: #f39c12;
}

.wp-favorites-toast-message {
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    font-weight: 500;
}

.wp-favorites-toast-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #999;
    transition: color 0.2s ease;
}

.wp-favorites-toast-close:hover {
    color: #666;
}

/* ==========================================================================
   Favorites List Styles
   ========================================================================== */

.wp-favorites-list {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.wp-favorites-list.columns-1 {
    grid-template-columns: 1fr;
}

.wp-favorites-list.columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.wp-favorites-list.columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.wp-favorites-list.columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

.wp-favorites-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.wp-favorites-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.wp-favorites-item-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
}

.wp-favorites-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wp-favorites-item:hover .wp-favorites-item-image img {
    transform: scale(1.05);
}

.wp-favorites-item-content {
    padding: 16px;
}

.wp-favorites-item-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.wp-favorites-item-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.2s ease;
}

.wp-favorites-item-title a:hover {
    color: #007cba;
}

.wp-favorites-item-price {
    font-size: 18px;
    font-weight: 700;
    color: #e74c3c;
    margin: 8px 0;
}

.wp-favorites-item-actions {
    display: flex;
    gap: 10px;
    margin-top: 12px;
}

.wp-favorites-remove-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    flex: 1;
}

.wp-favorites-remove-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.wp-favorites-view-btn {
    background: #007cba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.2s ease;
    flex: 1;
}

.wp-favorites-view-btn:hover {
    background: #005a87;
    transform: translateY(-1px);
    color: white;
}

/* Empty state */
.wp-favorites-empty {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.wp-favorites-empty-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 20px;
    opacity: 0.5;
}

.wp-favorites-empty-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: #333;
}

.wp-favorites-empty-message {
    font-size: 16px;
    line-height: 1.5;
    margin: 0 0 20px 0;
}

.wp-favorites-empty-action {
    background: #007cba;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
}

.wp-favorites-empty-action:hover {
    background: #005a87;
    color: white;
    transform: translateY(-1px);
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.wp-favorites-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.wp-favorites-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    animation: wp-favorites-spin 1s linear infinite;
    margin-right: 12px;
}

/* ==========================================================================
   Animations
   ========================================================================== */

@keyframes wp-favorites-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes wp-favorites-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .wp-favorites-list.columns-4 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wp-favorites-list.columns-3 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wp-favorites-toast-container {
        left: 20px;
        right: 20px;
    }
    
    .wp-favorites-toast {
        max-width: none;
    }
}

@media (max-width: 480px) {
    .wp-favorites-list.columns-4,
    .wp-favorites-list.columns-3,
    .wp-favorites-list.columns-2 {
        grid-template-columns: 1fr;
    }
    
    .wp-favorites-item-actions {
        flex-direction: column;
    }
    
    .wp-favorites-button {
        width: 36px;
        height: 36px;
    }
    
    .wp-favorites-button svg {
        width: 18px;
        height: 18px;
    }
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .wp-favorites-button,
    .wp-favorites-toast,
    .wp-favorites-item,
    .wp-favorites-item-image img {
        transition: none;
    }
    
    .wp-favorites-spinner {
        animation: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .wp-favorites-button {
        background: #fff;
        border: 2px solid #000;
    }
    
    .wp-favorites-toast {
        border: 2px solid #000;
    }
}

/* Focus styles for keyboard navigation */
.wp-favorites-button:focus-visible,
.wp-favorites-remove-btn:focus-visible,
.wp-favorites-view-btn:focus-visible {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .wp-favorites-button,
    .wp-favorites-toast-container {
        display: none;
    }
}
